-- <PERSON><PERSON><PERSON> hình endpoint bot server
local BOT_SERVER_ENDPOINT = 'http://localhost:3000/api/duty'

-- <PERSON><PERSON><PERSON> gửi dữ liệu đến bot server endpoint
local function SendToBotServer(identifier, name, job, grade, status, source)
    local data = {
        identifier = identifier,
        name = name,
        job = job,
        grade = grade,
        status = status,
        source = source,
        timestamp = os.date('%Y-%m-%d %H:%M:%S')
    }

    PerformHttpRequest(BOT_SERVER_ENDPOINT, function(err, text, headers)
        if err == 200 then
            print(('[Bot Server] Gửi duty log thành công: %s (%s) - %s'):format(name, job, status))
        else
            print(('[Bot Server] Lỗi gửi duty log: %s - %s'):format(err, text))
        end
    end, 'POST', json.encode(data), {
        ['Content-Type'] = 'application/json'
    })
end

-- C<PERSON><PERSON> nhật hàm LogDutyStatus để gửi đến bot server
local function LogDutyStatus(identifier, name, job, grade, status, source)
    -- Ghi log vào database (giữ nguyên code cũ)
    if MySQL then
        MySQL.Async.execute(
            'INSERT INTO duty_logs (identifier, name, job, grade, status, timestamp) VALUES (@identifier, @name, @job, @grade, @status, NOW())',
            {
                ['@identifier'] = identifier,
                ['@name'] = name,
                ['@job'] = job,
                ['@grade'] = grade,
                ['@status'] = status
            },
            function(rowsAffected)
                if rowsAffected > 0 then
                    print(('[Duty Log] Ghi log vào database thành công: %s (%s) - Job: %s, Grade: %s, Status: %s'):format(name, identifier, job, grade, status))
                else
                    print('[Duty Log] Lỗi khi ghi log vào database!')
                end
            end
        )
    end
    
    -- Gửi đến bot server endpoint
    SendToBotServer(identifier, name, job, grade, status, source)
end

-- Trong event handler 'duty:onoff', thêm source parameter khi gọi LogDutyStatus:
-- Ví dụ thay đổi:
-- LogDutyStatus(identifier, name, job, grade, 'off_duty')
-- Thành:
-- LogDutyStatus(identifier, name, job, grade, 'off_duty', _source)

--[[
Cách tích hợp vào code hiện tại:

1. Thêm cấu hình BOT_SERVER_ENDPOINT ở đầu file
2. Thêm hàm SendToBotServer
3. Cập nhật hàm LogDutyStatus để gọi SendToBotServer
4. Trong tất cả các lời gọi LogDutyStatus, thêm parameter _source

Ví dụ thay đổi trong event handler:
if status == 'off_duty' then
    if job == 'ambulance' then
        xPlayer.setJob('offambulance', grade)
        LogDutyStatus(identifier, name, job, grade, 'off_duty', _source) -- Thêm _source
        SendDiscordLog(identifier, name, job, grade, 'off_duty')
        TriggerClientEvent('duty:statusChanged', _source, 'off_duty')
    end
end
]]
