const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const mysql = require('mysql2/promise');
const moment = require('moment');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Database connection
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'duty_logs',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};

let pool;

// Initialize database connection
async function initDatabase() {
    try {
        pool = mysql.createPool(dbConfig);
        
        // Test connection
        const connection = await pool.getConnection();
        console.log('✅ Kết nối database thành công!');
        
        // Create table if not exists
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS duty_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                identifier VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                job VARCHAR(100) NOT NULL,
                grade INT NOT NULL,
                status ENUM('on_duty', 'off_duty') NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_identifier (identifier),
                INDEX idx_job (job),
                INDEX idx_timestamp (timestamp)
            )
        `);
        
        connection.release();
        console.log('✅ Bảng duty_logs đã được tạo/kiểm tra thành công!');
    } catch (error) {
        console.error('❌ Lỗi kết nối database:', error);
        process.exit(1);
    }
}

// Webhook URLs mapping
const webhooks = {
    police: process.env.POLICE_WEBHOOK,
    ambulance: process.env.AMBULANCE_WEBHOOK,
    mechanic: process.env.MECHANIC_WEBHOOK,
    army: process.env.ARMY_WEBHOOK
};

// Helper function to send Discord webhook
async function sendDiscordWebhook(job, data) {
    const webhook = webhooks[job];
    if (!webhook || webhook.includes('YOUR_') || webhook.includes('_HERE')) {
        console.log(`⚠️ Webhook chưa được cấu hình cho job: ${job}`);
        return;
    }

    const color = data.status === 'on_duty' ? 0x00FF00 : 0xFF0000;
    const embed = {
        title: `Duty Log - ${job.toUpperCase()}`,
        color: color,
        fields: [
            { name: 'Người chơi', value: data.name, inline: true },
            { name: 'Identifier', value: data.identifier, inline: true },
            { name: 'Công việc', value: job, inline: true },
            { name: 'Cấp bậc', value: data.grade.toString(), inline: true },
            { name: 'Trạng thái', value: data.status === 'on_duty' ? 'Bật Duty' : 'Tắt Duty', inline: true },
            { name: 'Thời gian', value: moment().format('YYYY-MM-DD HH:mm:ss'), inline: true }
        ],
        footer: { text: 'ESX Duty Log System' },
        timestamp: new Date().toISOString()
    };

    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(webhook, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: `${job.toUpperCase()} Duty Logs`,
                embeds: [embed]
            })
        });

        if (response.ok) {
            console.log(`✅ Gửi Discord webhook thành công cho job: ${job}`);
        } else {
            console.error(`❌ Lỗi gửi Discord webhook cho job ${job}:`, response.status, response.statusText);
        }
    } catch (error) {
        console.error(`❌ Lỗi gửi Discord webhook cho job ${job}:`, error);
    }
}

// Helper function to log duty status to database
async function logDutyStatus(identifier, name, job, grade, status) {
    try {
        const [result] = await pool.execute(
            'INSERT INTO duty_logs (identifier, name, job, grade, status, timestamp) VALUES (?, ?, ?, ?, ?, NOW())',
            [identifier, name, job, grade, status]
        );
        
        if (result.affectedRows > 0) {
            console.log(`✅ Ghi log database thành công: ${name} (${identifier}) - Job: ${job}, Grade: ${grade}, Status: ${status}`);
            return true;
        }
        return false;
    } catch (error) {
        console.error('❌ Lỗi ghi log vào database:', error);
        return false;
    }
}

// Routes

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Main endpoint to receive duty status
app.post('/api/duty', async (req, res) => {
    try {
        const { identifier, name, job, grade, status, source } = req.body;
        
        // Validate required fields
        if (!identifier || !name || !job || grade === undefined || !status) {
            return res.status(400).json({
                error: 'Thiếu thông tin bắt buộc',
                required: ['identifier', 'name', 'job', 'grade', 'status']
            });
        }

        // Validate status
        if (!['on_duty', 'off_duty'].includes(status)) {
            return res.status(400).json({
                error: 'Status không hợp lệ',
                validStatus: ['on_duty', 'off_duty']
            });
        }

        // Log to database
        const logSuccess = await logDutyStatus(identifier, name, job, grade, status);
        
        // Send to Discord webhook
        await sendDiscordWebhook(job, { identifier, name, job, grade, status });
        
        res.json({
            success: true,
            message: 'Đã nhận và xử lý thông tin duty thành công',
            data: {
                identifier,
                name,
                job,
                grade,
                status,
                timestamp: new Date().toISOString(),
                logged: logSuccess
            }
        });

        console.log(`📝 Nhận duty log: ${name} (${job}) - ${status}`);
        
    } catch (error) {
        console.error('❌ Lỗi xử lý duty request:', error);
        res.status(500).json({
            error: 'Lỗi server nội bộ',
            message: error.message
        });
    }
});

// Get duty logs endpoint
app.get('/api/duty/logs', async (req, res) => {
    try {
        const { job, limit = 50, offset = 0 } = req.query;
        
        let query = 'SELECT * FROM duty_logs';
        let params = [];
        
        if (job) {
            query += ' WHERE job = ?';
            params.push(job);
        }
        
        query += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));
        
        const [rows] = await pool.execute(query, params);
        
        res.json({
            success: true,
            data: rows,
            count: rows.length
        });
        
    } catch (error) {
        console.error('❌ Lỗi lấy duty logs:', error);
        res.status(500).json({
            error: 'Lỗi server nội bộ',
            message: error.message
        });
    }
});

// Get duty statistics
app.get('/api/duty/stats', async (req, res) => {
    try {
        const [stats] = await pool.execute(`
            SELECT 
                job,
                COUNT(*) as total_logs,
                SUM(CASE WHEN status = 'on_duty' THEN 1 ELSE 0 END) as on_duty_count,
                SUM(CASE WHEN status = 'off_duty' THEN 1 ELSE 0 END) as off_duty_count
            FROM duty_logs 
            WHERE DATE(timestamp) = CURDATE()
            GROUP BY job
        `);
        
        res.json({
            success: true,
            data: stats
        });
        
    } catch (error) {
        console.error('❌ Lỗi lấy duty stats:', error);
        res.status(500).json({
            error: 'Lỗi server nội bộ',
            message: error.message
        });
    }
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint không tồn tại',
        path: req.originalUrl
    });
});

// Error handler
app.use((error, req, res, next) => {
    console.error('❌ Unhandled error:', error);
    res.status(500).json({
        error: 'Lỗi server nội bộ',
        message: error.message
    });
});

// Start server
async function startServer() {
    await initDatabase();
    
    app.listen(PORT, () => {
        console.log(`🚀 Server đang chạy tại http://localhost:${PORT}`);
        console.log(`📊 Health check: http://localhost:${PORT}/health`);
        console.log(`📝 Duty endpoint: http://localhost:${PORT}/api/duty`);
        console.log(`📋 Logs endpoint: http://localhost:${PORT}/api/duty/logs`);
        console.log(`📈 Stats endpoint: http://localhost:${PORT}/api/duty/stats`);
    });
}

startServer().catch(console.error);
