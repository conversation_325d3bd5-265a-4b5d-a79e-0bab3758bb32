# Med Duty Bot Server

Bot server endpoint để nhận thông tin on/off duty của med (ambulance) và các job khác.

## Tính năng

- ✅ Nhận thông tin duty từ các nguồn bên ngoài qua API
- ✅ Lưu trữ log duty vào database MySQL
- ✅ Gửi thông báo lên Discord webhook
- ✅ API để xem logs và thống kê
- ✅ Endpoint test để kiểm tra hoạt động

## Cài đặt

### 1. Cài đặt dependencies

```bash
npm install
```

### 2. Cấu hình database

Tạo database MySQL và cập nhật thông tin trong file `.env`:

```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=duty_logs
```

### 3. Cấu hình Discord Webhooks

Cập nhật các webhook URL trong file `.env`:

```env
POLICE_WEBHOOK=https://discord.com/api/webhooks/your_webhook_url
AMBULANCE_WEBHOOK=https://discord.com/api/webhooks/your_webhook_url
MECHANIC_WEBHOOK=https://discord.com/api/webhooks/your_webhook_url
```

### 4. Chạy server

```bash
# Development mode
npm run dev

# Production mode
npm start
```

Server sẽ chạy tại: `http://localhost:3000`

## API Endpoints

### 1. Health Check
```
GET /health
```

### 2. Nhận thông tin duty
```
POST /api/duty
Content-Type: application/json

{
  "identifier": "steam:110000123456789",
  "name": "Player Name",
  "job": "ambulance",
  "grade": 2,
  "status": "on_duty"
}
```

**Các job được hỗ trợ:** `police`, `police2`, `ambulance`, `mechanic`, `tiembanh`, `army`

**Status:** `on_duty` hoặc `off_duty`

### 3. Xem logs
```
GET /api/duty/logs?job=ambulance&limit=50&offset=0
```

### 4. Xem thống kê
```
GET /api/duty/stats
```

### 5. Test endpoint
```
POST /api/duty/test
```

## Cách sử dụng

### Gửi thông tin duty từ bên ngoài

Bạn có thể gửi thông tin duty từ:

1. **Webhook từ Discord bot khác**
2. **API call từ web application**
3. **Script tự động**
4. **Manual testing**

Ví dụ sử dụng curl:

```bash
curl -X POST http://localhost:3000/api/duty \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "steam:110000123456789",
    "name": "Bác sĩ ABC",
    "job": "ambulance",
    "grade": 3,
    "status": "on_duty"
  }'
```

### Xem logs qua API

```bash
# Xem tất cả logs
curl http://localhost:3000/api/duty/logs

# Xem logs của ambulance
curl http://localhost:3000/api/duty/logs?job=ambulance

# Xem thống kê hôm nay
curl http://localhost:3000/api/duty/stats
```

## Database Schema

Bảng `duty_logs` sẽ được tự động tạo với cấu trúc:

```sql
CREATE TABLE duty_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    job VARCHAR(100) NOT NULL,
    grade INT NOT NULL,
    status ENUM('on_duty', 'off_duty') NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_identifier (identifier),
    INDEX idx_job (job),
    INDEX idx_timestamp (timestamp)
);
```

## Logs

Server sẽ ghi log các hoạt động:

- ✅ Kết nối database thành công
- 📝 Nhận duty log mới
- 🔄 Gửi Discord webhook
- ❌ Các lỗi xảy ra

## Tích hợp với hệ thống khác

### Từ Discord Bot

Nếu bạn có Discord bot khác, có thể gửi thông tin duty qua API:

```javascript
// Trong Discord bot
const axios = require('axios');

async function sendDutyLog(playerData) {
    try {
        await axios.post('http://localhost:3000/api/duty', {
            identifier: playerData.identifier,
            name: playerData.name,
            job: playerData.job,
            grade: playerData.grade,
            status: playerData.status
        });
    } catch (error) {
        console.error('Lỗi gửi duty log:', error);
    }
}
```

### Từ Web Application

```javascript
// Từ frontend web app
fetch('http://localhost:3000/api/duty', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        identifier: 'steam:110000123456789',
        name: 'Player Name',
        job: 'ambulance',
        grade: 2,
        status: 'on_duty'
    })
});
```

## Troubleshooting

### Lỗi kết nối database
- Kiểm tra thông tin database trong `.env`
- Đảm bảo MySQL server đang chạy
- Kiểm tra quyền truy cập database

### Webhook không hoạt động
- Kiểm tra URL webhook trong `.env`
- Đảm bảo webhook URL hợp lệ và có quyền gửi tin nhắn

### Port đã được sử dụng
- Thay đổi PORT trong `.env`
- Hoặc dừng process đang sử dụng port 3000
