// Test script để kiểm tra API endpoints
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data
const testDutyData = [
    {
        identifier: 'steam:110000123456789',
        name: '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
        job: 'ambulance',
        grade: 3,
        status: 'on_duty'
    },
    {
        identifier: 'steam:110000987654321',
        name: 'Cảnh sát Trần Thị B',
        job: 'police',
        grade: 2,
        status: 'on_duty'
    },
    {
        identifier: 'steam:110000123456789',
        name: '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
        job: 'ambulance',
        grade: 3,
        status: 'off_duty'
    }
];

async function testHealthCheck() {
    try {
        console.log('🔍 Testing health check...');
        const response = await axios.get(`${BASE_URL}/health`);
        console.log('✅ Health check passed:', response.data);
        return true;
    } catch (error) {
        console.error('❌ Health check failed:', error.message);
        return false;
    }
}

async function testDutyEndpoint() {
    try {
        console.log('\n📝 Testing duty endpoint...');
        
        for (const data of testDutyData) {
            console.log(`\nGửi duty log: ${data.name} (${data.job}) - ${data.status}`);
            
            const response = await axios.post(`${BASE_URL}/api/duty`, data);
            console.log('✅ Response:', response.data);
            
            // Đợi 1 giây giữa các request
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        return true;
    } catch (error) {
        console.error('❌ Duty endpoint test failed:', error.response?.data || error.message);
        return false;
    }
}

async function testGetLogs() {
    try {
        console.log('\n📋 Testing get logs endpoint...');
        
        // Test get all logs
        const allLogs = await axios.get(`${BASE_URL}/api/duty/logs`);
        console.log(`✅ All logs (${allLogs.data.count} records):`, allLogs.data.data.slice(0, 3));
        
        // Test get ambulance logs
        const ambulanceLogs = await axios.get(`${BASE_URL}/api/duty/logs?job=ambulance`);
        console.log(`✅ Ambulance logs (${ambulanceLogs.data.count} records):`, ambulanceLogs.data.data.slice(0, 2));
        
        return true;
    } catch (error) {
        console.error('❌ Get logs test failed:', error.response?.data || error.message);
        return false;
    }
}

async function testGetStats() {
    try {
        console.log('\n📈 Testing get stats endpoint...');
        
        const stats = await axios.get(`${BASE_URL}/api/duty/stats`);
        console.log('✅ Stats:', stats.data);
        
        return true;
    } catch (error) {
        console.error('❌ Get stats test failed:', error.response?.data || error.message);
        return false;
    }
}

async function testInvalidData() {
    try {
        console.log('\n🚫 Testing invalid data...');
        
        // Test missing required fields
        const invalidData = {
            name: 'Test Player',
            job: 'ambulance'
            // Missing identifier, grade, status
        };
        
        const response = await axios.post(`${BASE_URL}/api/duty`, invalidData);
        console.log('❌ Should have failed but got:', response.data);
        return false;
    } catch (error) {
        if (error.response?.status === 400) {
            console.log('✅ Invalid data correctly rejected:', error.response.data);
            return true;
        } else {
            console.error('❌ Unexpected error:', error.response?.data || error.message);
            return false;
        }
    }
}

async function testTestEndpoint() {
    try {
        console.log('\n🧪 Testing test endpoint...');
        
        const response = await axios.post(`${BASE_URL}/api/duty/test`);
        console.log('✅ Test endpoint response:', response.data);
        
        return true;
    } catch (error) {
        console.error('❌ Test endpoint failed:', error.response?.data || error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Bắt đầu test API endpoints...\n');
    
    const tests = [
        { name: 'Health Check', fn: testHealthCheck },
        { name: 'Duty Endpoint', fn: testDutyEndpoint },
        { name: 'Get Logs', fn: testGetLogs },
        { name: 'Get Stats', fn: testGetStats },
        { name: 'Invalid Data', fn: testInvalidData },
        { name: 'Test Endpoint', fn: testTestEndpoint }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            const result = await test.fn();
            if (result) {
                passed++;
                console.log(`✅ ${test.name}: PASSED`);
            } else {
                failed++;
                console.log(`❌ ${test.name}: FAILED`);
            }
        } catch (error) {
            failed++;
            console.log(`❌ ${test.name}: ERROR -`, error.message);
        }
        
        console.log('─'.repeat(50));
    }
    
    console.log(`\n📊 Test Results:`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 Tất cả tests đều PASSED! API hoạt động tốt.');
    } else {
        console.log('\n⚠️ Có một số tests FAILED. Kiểm tra lại server và cấu hình.');
    }
}

// Chạy tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testHealthCheck,
    testDutyEndpoint,
    testGetLogs,
    testGetStats,
    testInvalidData,
    testTestEndpoint,
    runAllTests
};
